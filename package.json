{"name": "vite-express-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "nodemon src/server/main.js -w src/server", "start": "NODE_ENV=production node src/server/main.js", "build": "vite build", "db:init": "node src/server/scripts/init-db.js", "db:reset": "node src/server/scripts/init-db.js --force --confirm", "db:migrate": "node src/server/scripts/init-db.js --alter"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.1.12", "@types/js-cookie": "^3.0.6", "axios": "^1.11.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookie-parser": "^1.4.7", "dotenv": "^17.2.1", "express": "^5.1.0", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.541.0", "next-themes": "^0.4.6", "pg": "^8.16.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.62.0", "react-router-dom": "^7.8.2", "sequelize": "^6.37.7", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.12", "vite-express": "*", "zod": "^4.1.3"}, "devDependencies": {"@types/node": "^24.3.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "nodemon": "^3.1.10", "tw-animate-css": "^1.3.7", "vite": "^6.3.3"}}