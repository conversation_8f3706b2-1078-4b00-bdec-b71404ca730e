#!/usr/bin/env node

import db from "../config/db.js";

const initializeDatabase = async () => {
  try {
    console.log("🔄 Starting database initialization...");

    // Initialize database
    await db.initializeDatabase();

    // Sync database (force: true will drop existing tables)
    const shouldForceSync = process.argv.includes("--force");

    if (shouldForceSync) {
      console.log(
        "⚠️  WARNING: Forcing database sync (this will drop existing tables)"
      );
      const confirmed = process.argv.includes("--confirm");
      if (!confirmed) {
        console.log("❌ Please add --confirm flag to force sync");
        process.exit(1);
      }
    }

    await db.sequelize.sync({
      force: shouldForceSync,
      alter: !shouldForceSync,
    });

    console.log("✅ Database initialization completed successfully!");

    // Close connection
    await db.closeConnection();
    process.exit(0);
  } catch (error) {
    console.error("❌ Database initialization failed:", error);
    process.exit(1);
  }
};

initializeDatabase();
