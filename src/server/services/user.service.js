import db from "../config/db.js";

const createUser = async (userData) => {
  try {
    const user = await db.User.create(userData);
    return user;
  } catch (error) {
    if (error.name === "SequelizeUniqueConstraintError") {
      const field = error.errors[0].path;
      throw new Error(`${field} already exists`);
    }
    if (error.name === "SequelizeValidationError") {
      const message = error.errors.map((err) => err.message).join(", ");
      throw new Error(`Validation error: ${message}`);
    }
    throw error;
  }
};

const getAllUsers = async (options = {}) => {
  try {
    const {
      page = 1,
      limit = 10,
      includeInactive = false,
      search = "",
    } = options;

    const offset = (page - 1) * limit;
    const whereClause = {};

    if (!includeInactive) {
      whereClause.isActive = true;
    }

    if (search) {
      whereClause[db.Sequelize.Op.or] = [
        { userName: { [db.Sequelize.Op.iLike]: `%${search}%` } },
        { email: { [db.Sequelize.Op.iLike]: `%${search}%` } },
        { firstName: { [db.Sequelize.Op.iLike]: `%${search}%` } },
        { lastName: { [db.Sequelize.Op.iLike]: `%${search}%` } },
      ];
    }

    const { count, rows } = await db.User.findAndCountAll({
      where: whereClause,
      limit,
      offset,
      order: [["createdAt", "DESC"]],
    });

    return {
      users: rows,
      totalCount: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
      hasNextPage: page * limit < count,
      hasPrevPage: page > 1,
    };
  } catch (error) {
    throw error;
  }
};

const getUserById = async (id) => {
  try {
    const user = await db.User.findByPk(id, {
      include: [
        {
          model: db.RefreshToken,
          as: "refreshTokens",
          where: { isRevoked: false },
          required: false,
        },
      ],
    });
    return user;
  } catch (error) {
    throw error;
  }
};

const updateUser = async (id, userData) => {
  try {
    const user = await db.User.findByPk(id);
    if (!user) throw new Error("User not found");

    await user.update(userData);
    return user;
  } catch (error) {
    if (error.name === "SequelizeUniqueConstraintError") {
      const field = error.errors[0].path;
      throw new Error(`${field} already exists`);
    }
    if (error.name === "SequelizeValidationError") {
      const message = error.errors.map((err) => err.message).join(", ");
      throw new Error(`Validation error: ${message}`);
    }
    throw error;
  }
};

const deleteUser = async (id) => {
  try {
    const user = await db.User.findByPk(id);
    if (!user) throw new Error("User not found");

    // Soft delete (if paranoid is enabled)
    await user.destroy();
    return user;
  } catch (error) {
    throw error;
  }
};

export default {
  createUser,
  getAllUsers,
  getUserById,
  updateUser,
  deleteUser,
};
