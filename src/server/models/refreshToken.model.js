import { DataTypes } from "sequelize";

// Export a function that defines the model
export default (sequelize) => {
  const RefreshToken = sequelize.define(
    "RefreshToken",
    {
      id: {
        type: DataTypes.BIGINT.UNSIGNED,
        autoIncrement: true,
        primaryKey: true,
      },
      token: {
        type: DataTypes.TEXT,
        allowNull: false,
        unique: true,
      },
      expiresAt: {
        type: DataTypes.DATE,
        allowNull: false,
        validate: {
          isAfter: new Date().toISOString(),
        },
      },
      isRevoked: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      tableName: "refresh_tokens",
      timestamps: true,
    }
  );

  // Instance methods
  RefreshToken.prototype.isExpired = function () {
    return new Date() > this.expiresAt;
  };

  RefreshToken.prototype.isValid = function () {
    return !this.isRevoked && !this.isExpired();
  };

  return RefreshToken;
};
