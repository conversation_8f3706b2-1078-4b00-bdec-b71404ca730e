import express from "express";
import cookieParser from "cookie-parser";
import db from "./config/db.js";
import config from "./config/config.js";
import ViteExpress from "vite-express";
import userRoutes from "./routers/userRoutes.js";
import authRoutes from "./routers/authRoutes.js";

const app = express();
const { server } = config;

// Middleware
app.use(express.json({ limit: "10mb" }));
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());

// Routes
app.use("/api/users", userRoutes);
app.use("/api/auth", authRoutes);

// Health check endpoint
app.get("/api/health", (req, res) => {
  res.status(200).json({
    status: "OK",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
  });
});

// Error handling middleware
app.use((error, req, res, next) => {
  console.error("Unhandled error:", error);
  res.status(500).json({
    success: false,
    error:
      server.nodeEnv === "production" ? "Internal server error" : error.message,
  });
});

// Graceful shutdown
const gracefulShutdown = async (signal) => {
  console.log(`\n${signal} received. Starting graceful shutdown...`);

  try {
    await db.closeConnection();
    process.exit(0);
  } catch (error) {
    console.error("Error during graceful shutdown:", error);
    process.exit(1);
  }
};

process.on("SIGINT", () => gracefulShutdown("SIGINT"));
process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));

// Start server with database initialization
const startServer = async () => {
  try {
    // Initialize database
    await db.initializeDatabase();

    // Sync database
    await db.sequelize.sync({ force: false });
    console.log("✅ Database synchronized successfully.");

    // Start server
    ViteExpress.listen(app, server.port, () => {
      console.log(`🚀 Server is running on port ${server.port}`);
      console.log(
        `📊 Health check: http://localhost:${server.port}/api/health`
      );
      console.log(`🔧 Environment: ${server.nodeEnv}`);
    });
  } catch (error) {
    console.error("❌ Failed to start server:", error);
    process.exit(1);
  }
};

startServer();
