import { Sequelize } from "sequelize";
import User from "../models/user.model.js";
import RefreshToken from "../models/refreshToken.model.js";
import config from "./config.js";

const { database: dbConfig, server } = config;

// Sequelize instance with optimized configuration
const sequelize = new Sequelize(
  dbConfig.name,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    logging: server.nodeEnv === "development" ? console.log : false,
    pool: {
      max: 10,
      min: 0,
      acquire: 30000,
      idle: 10000,
    },
    retry: {
      max: 3,
    },
    define: {
      freezeTableName: true,
      underscored: true,
    },
  }
);

const connectDb = async (retries = 3) => {
  for (let i = 0; i < retries; i++) {
    try {
      await sequelize.authenticate();
      console.log("✅ Database connection established successfully.");
      return true;
    } catch (error) {
      console.error(
        `❌ Database connection attempt ${i + 1} failed:`,
        error.message
      );
      if (i === retries - 1) {
        console.error("❌ All database connection attempts failed");
        throw error;
      }
      await new Promise((resolve) => setTimeout(resolve, 2000 * (i + 1))); // Exponential backoff
    }
  }
};

// Initialize database and models
const initializeDatabase = async () => {
  try {
    // Test connection
    await connectDb();

    // Initialize models
    db.User = User(sequelize);
    db.RefreshToken = RefreshToken(sequelize);

    // Define associations
    setupAssociations();

    return db;
  } catch (error) {
    console.error("❌ Database initialization failed:", error);
    throw error;
  }
};

// Setup model associations
const setupAssociations = () => {
  // A User can have many Refresh Tokens
  db.User.hasMany(db.RefreshToken, {
    foreignKey: {
      name: "userId",
      allowNull: false,
    },
    onDelete: "CASCADE",
    as: "refreshTokens",
  });

  // A Refresh Token belongs to one User
  db.RefreshToken.belongsTo(db.User, {
    foreignKey: {
      name: "userId",
      allowNull: false,
    },
    as: "user",
  });
};

// Graceful shutdown
const closeConnection = async () => {
  try {
    await sequelize.close();
    console.log("✅ Database connection closed successfully.");
  } catch (error) {
    console.error("❌ Error closing database connection:", error);
  }
};

// A practical way to bundle everything for easy access
const db = {
  Sequelize,
  sequelize,
  connectDb,
  initializeDatabase,
  closeConnection,
  // Models will be added by initializeDatabase
  User: null,
  RefreshToken: null,
};
export default db;
