import dotenv from "dotenv";

// Load environment variables from .env file
dotenv.config();

const config = {
  // Database configuration
  database: {
    name: process.env.DB_NAME || "test_db",
    username: process.env.DB_USER || "apple",
    password: process.env.DB_PASSWORD || "",
    host: process.env.DB_HOST || "localhost",
    port: parseInt(process.env.DB_PORT) || 5432,
    dialect: process.env.DB_DIALECT || "postgres",
  },

  // Server configuration
  server: {
    port: parseInt(process.env.PORT) || 3000,
    nodeEnv: process.env.NODE_ENV || "development",
  },

  // JWT configuration (for future use)
  jwt: {
    secret: process.env.JWT_SECRET || "your-secret-key-here",
    expiresIn: process.env.JWT_EXPIRES_IN || "2m",
    refreshTokenExpiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || "7d",
  },
};

export default config;
