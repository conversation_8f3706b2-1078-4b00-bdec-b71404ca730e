import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import config from "../config/config.js";
import db from "../config/db.js";

const authController = {
  // Login user
  login: async (req, res) => {
    try {
      const { email, password } = req.body;

      // Validate input
      if (!email || !password) {
        return res.status(400).json({
          success: false,
          error: "Email and password are required",
        });
      }

      // Find user by email
      const user = await db.User.findOne({
        where: { email: email.toLowerCase() },
      });

      if (!user) {
        return res.status(401).json({
          success: false,
          error: "Invalid email or password",
        });
      }

      // Check if user is active
      if (!user.isActive) {
        return res.status(401).json({
          success: false,
          error: "Account is deactivated. Please contact support.",
        });
      }

      // Verify password
      const isValidPassword = await user.comparePassword(password);
      if (!isValidPassword) {
        return res.status(401).json({
          success: false,
          error: "Invalid email or password",
        });
      }

      // Generate JWT tokens
      const accessToken = jwt.sign(
        {
          userId: user.id,
          email: user.email,
          userName: user.userName,
        },
        config.jwt.secret,
        { expiresIn: config.jwt.expiresIn }
      );

      const refreshToken = jwt.sign({ userId: user.id }, config.jwt.secret, {
        expiresIn: config.jwt.refreshTokenExpiresIn,
      });

      // Store refresh token in database
      await db.RefreshToken.create({
        token: refreshToken,
        userId: user.id,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      });

      // Set HTTP-only cookie for refresh token
      res.cookie("refreshToken", refreshToken, {
        httpOnly: true,
        secure: config.server.nodeEnv === "production",
        sameSite: "strict",
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      });

      res.json({
        success: true,
        data: {
          token: accessToken,
          user: {
            id: user.id,
            userName: user.userName,
            email: user.email,
            firstName: user.firstName,
            lastName: user.lastName,
            isActive: user.isActive,
          },
        },
        message: "Login successful",
      });
    } catch (error) {
      console.error("Login error:", error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
      });
    }
  },

  // Logout user
  logout: async (req, res) => {
    try {
      const refreshToken = req.cookies.refreshToken;

      if (refreshToken) {
        // Remove refresh token from database
        await db.RefreshToken.destroy({
          where: { token: refreshToken },
        });
      }

      // Clear refresh token cookie
      res.clearCookie("refreshToken");

      res.json({
        success: true,
        message: "Logout successful",
      });
    } catch (error) {
      console.error("Logout error:", error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
      });
    }
  },

  // Refresh access token
  refreshToken: async (req, res) => {
    try {
      const refreshToken = req.cookies.refreshToken;

      if (!refreshToken) {
        return res.status(401).json({
          success: false,
          error: "Refresh token not provided",
        });
      }

      // Verify refresh token
      const decoded = jwt.verify(refreshToken, config.jwt.secret);

      // Find refresh token in database
      const storedToken = await db.RefreshToken.findOne({
        where: { token: refreshToken },
        include: [{ model: db.User, as: "user" }],
      });

      if (!storedToken || !storedToken.isValid()) {
        return res.status(401).json({
          success: false,
          error: "Invalid or expired refresh token",
        });
      }

      // Generate new access token
      const accessToken = jwt.sign(
        {
          userId: storedToken.user.id,
          email: storedToken.user.email,
          userName: storedToken.user.userName,
        },
        config.jwt.secret,
        { expiresIn: config.jwt.expiresIn }
      );

      res.json({
        success: true,
        data: {
          token: accessToken,
        },
      });
    } catch (error) {
      console.error("Refresh token error:", error);
      res.status(401).json({
        success: false,
        error: "Invalid refresh token",
      });
    }
  },

  // Get user profile
  getProfile: async (req, res) => {
    try {
      const user = await db.User.findByPk(req.user.userId);

      if (!user) {
        return res.status(404).json({
          success: false,
          error: "User not found",
        });
      }

      res.json({
        success: true,
        data: {
          id: user.id,
          userName: user.userName,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          isActive: user.isActive,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt,
        },
      });
    } catch (error) {
      console.error("Get profile error:", error);
      res.status(500).json({
        success: false,
        error: "Internal server error",
      });
    }
  },

  // Update user profile
  updateProfile: async (req, res) => {
    try {
      const { firstName, lastName, userName } = req.body;
      const user = await db.User.findByPk(req.user.userId);

      if (!user) {
        return res.status(404).json({
          success: false,
          error: "User not found",
        });
      }

      const updatedUser = await user.update({
        firstName: firstName || user.firstName,
        lastName: lastName || user.lastName,
        userName: userName || user.userName,
      });

      res.json({
        success: true,
        data: {
          id: updatedUser.id,
          userName: updatedUser.userName,
          email: updatedUser.email,
          firstName: updatedUser.firstName,
          lastName: updatedUser.lastName,
          isActive: updatedUser.isActive,
        },
        message: "Profile updated successfully",
      });
    } catch (error) {
      console.error("Update profile error:", error);

      if (error.name === "SequelizeUniqueConstraintError") {
        return res.status(400).json({
          success: false,
          error: "Username already exists",
        });
      }

      res.status(500).json({
        success: false,
        error: "Internal server error",
      });
    }
  },
};

export default authController;
