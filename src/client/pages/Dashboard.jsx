import React from "react";
import { useAuth } from "../contexts/AuthContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import { Users, UserPlus, Activity, TrendingUp } from "lucide-react";

const Dashboard = () => {
  const { user } = useAuth();

  const stats = [
    {
      title: "Total Users",
      value: "1,234",
      description: "+10% from last month",
      icon: Users,
      variant: "default",
    },
    {
      title: "New Users",
      value: "89",
      description: "+5% from last week",
      icon: UserPlus,
      variant: "secondary",
    },
    {
      title: "Active Users",
      value: "892",
      description: "+2% from yesterday",
      icon: Activity,
      variant: "accent",
    },
    {
      title: "Growth Rate",
      value: "12.5%",
      description: "+0.5% from last month",
      icon: TrendingUp,
      variant: "muted",
    },
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <Card className="p-6">
        <h1 className="text-3xl font-bold text-foreground mb-2">
          Welcome back, {user?.firstName}!
        </h1>
        <p className="text-muted-foreground">
          Here's what's happening with your application today.
        </p>
      </Card>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index} className="hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  {stat.title}
                </CardTitle>
                <div
                  className={`p-2 rounded-full ${
                    stat.variant === "default"
                      ? "bg-primary/10"
                      : stat.variant === "secondary"
                      ? "bg-secondary/10"
                      : stat.variant === "accent"
                      ? "bg-accent/10"
                      : "bg-muted"
                  }`}
                >
                  <Icon
                    className={`h-4 w-4 ${
                      stat.variant === "default"
                        ? "text-primary"
                        : stat.variant === "secondary"
                        ? "text-secondary-foreground"
                        : stat.variant === "accent"
                        ? "text-accent-foreground"
                        : "text-muted-foreground"
                    }`}
                  />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-foreground">
                  {stat.value}
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {stat.description}
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>
              Latest user actions and system events
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-chart-2 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-foreground">
                    New user registered
                  </p>
                  <p className="text-xs text-muted-foreground">2 minutes ago</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-chart-1 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-foreground">
                    User profile updated
                  </p>
                  <p className="text-xs text-muted-foreground">5 minutes ago</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-chart-3 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-foreground">
                    System maintenance completed
                  </p>
                  <p className="text-xs text-muted-foreground">
                    10 minutes ago
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common tasks and shortcuts</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <button className="p-4 text-left border border-border rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors">
                <UserPlus className="h-6 w-6 text-chart-1 mb-2" />
                <h3 className="font-medium text-foreground">Add User</h3>
                <p className="text-xs text-muted-foreground">
                  Create new user account
                </p>
              </button>
              <button className="p-4 text-left border border-border rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors">
                <Users className="h-6 w-6 text-chart-2 mb-2" />
                <h3 className="font-medium text-foreground">View Users</h3>
                <p className="text-xs text-muted-foreground">
                  Manage user accounts
                </p>
              </button>
              <button className="p-4 text-left border border-border rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors">
                <Activity className="h-6 w-6 text-chart-3 mb-2" />
                <h3 className="font-medium text-foreground">Analytics</h3>
                <p className="text-xs text-muted-foreground">
                  View detailed reports
                </p>
              </button>
              <button className="p-4 text-left border border-border rounded-lg hover:bg-accent hover:text-accent-foreground transition-colors">
                <TrendingUp className="h-6 w-6 text-chart-4 mb-2" />
                <h3 className="font-medium text-foreground">Reports</h3>
                <p className="text-xs text-muted-foreground">
                  Generate system reports
                </p>
              </button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Dashboard;
