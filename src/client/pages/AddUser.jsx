import React, { useEffect } from "react";
import BaseLayouts from "../components/layouts/BaseLayouts";
import TitleText from "../components/TitleText";
import { Button } from "@/components/ui/button";
import { DeleteIcon, EditIcon } from "lucide-react";
import { Input } from "@/components/ui/input";

export const AddUser = () => {
  const [name, setName] = React.useState("");
  const [email, setEmail] = React.useState("");
  const [users, setUsers] = React.useState([]);
  const [loadingAdd, setLoadingAdd] = React.useState(false);
  const [isEditing, setIsEditing] = React.useState(false);
  const [userIdForEdit, setUserIdForEdit] = React.useState(null);

  const BASE_URL = "http://localhost:3000/api";

  const fetchAllUsers = () => {
    fetch(`${BASE_URL}/users`)
      .then((res) => res.json())
      .then((data) => {
        console.log("Fetched users:", data);
        const users = data.data.users;
        setUsers(users);
      })
      .catch((error) => console.error("Error fetching users:", error));
  };

  useEffect(() => {
    fetchAllUsers();
  }, []);

  const handleAddUser = () => {
    if (!name.trim() || !email.trim()) return;
    setLoadingAdd(true);
    if (isEditing) {
      handleEditUser();
      return;
    }
    fetch(`${BASE_URL}/users`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ name: name.trim(), email: email.trim() }),
    })
      .then((res) => res.json())
      .then((data) => {
        // Optimistic prepend
        setUsers((prev) => [data, ...prev]);
      })
      .catch((error) => console.error("Error adding user:", error))
      .finally(() => {
        setName("");
        setEmail("");
        setLoadingAdd(false);
      });
  };

  const handleDeleteUser = (id) => {
    // Optimistic update
    setUsers((prev) => prev.filter((u) => u.id !== id));
    fetch(`${BASE_URL}/users/${id}`, { method: "DELETE" })
      .then((res) => {
        if (!res.ok) throw new Error("Failed to delete");
      })
      .catch((error) => {
        console.error("Error deleting user:", error);
        // Re-fetch to correct optimistic removal if failed
        fetchAllUsers();
      });
  };

  const setEditSetting = (id) => {
    const user = users.find((u) => u.id === id);
    if (user) {
      setName(user.name);
      setEmail(user.email);
      setUserIdForEdit(user.id);
      setIsEditing(true);
    }
  };

  const handleEditUser = () => {
    if (!name.trim() || !email.trim()) return;
    setLoadingAdd(true);
    fetch(`${BASE_URL}/users/${userIdForEdit}`, {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ name: name.trim(), email: email.trim() }),
    })
      .then((res) => res.json())
      .then((data) => {
        setUsers((prev) => prev.map((u) => (u.id === data.id ? data : u)));
      })
      .catch((error) => console.error("Error editing user:", error))
      .finally(() => {
        setName("");
        setEmail("");
        setLoadingAdd(false);
        setIsEditing(false);
        setUserIdForEdit(null);
      });
  };

  return (
    <BaseLayouts>
      <TitleText text="Add User" />
      <div className="flex flex-col sm:flex-row w-full gap-4 mb-4">
        <div className="sm:w-1/2 flex flex-col sm:flex-row items-center gap-4 w-full">
          <Input
            type="text"
            placeholder="Enter user name"
            value={name}
            onChange={(e) => setName(e.target.value)}
          />
          <Input
            type="email"
            placeholder="Enter user email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
          />
        </div>
        <div className="flex flex-col px-4 sm:px-0 sm:flex-row sm:items-center justify-end gap-4 ">
          <Button
            onClick={handleAddUser}
            disabled={loadingAdd || !name.trim() || !email.trim()}
          >
            {isEditing ? "Edit User" : "Add User"}
          </Button>
          {isEditing && (
            <Button
              onClick={() => {
                setIsEditing(false);
                setUserIdForEdit(null);
                setName("");
                setEmail("");
              }}
            >
              Cancel
            </Button>
          )}
        </div>
      </div>
      <div>
        <h2 className="font-semibold mb-2">Users List</h2>
        <ul className="space-y-2">
          {users?.map((user, i) => (
            <li
              key={i}
              className="flex items-center justify-between border rounded px-3 py-2"
            >
              <span>
                {user.name} - {user.email}
              </span>
              <div className="flex gap-2 items-center h-full">
                <Button
                  variant="outline"
                  onClick={() => setEditSetting(user.id)}
                  title="Edit user"
                >
                  <EditIcon className="w-4 h-4" />
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => handleDeleteUser(user.id)}
                  title="Delete user"
                >
                  <DeleteIcon className="w-4 h-4" />
                </Button>
              </div>
            </li>
          ))}
          {users.length === 0 && (
            <li className="text-sm text-gray-500">No users found.</li>
          )}
        </ul>
      </div>
    </BaseLayouts>
  );
};
