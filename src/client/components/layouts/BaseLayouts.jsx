import React from "react";
import HeaderLayout from "./HeaderLayout";
import { ModeToggle } from "@/components/mode-toggle";

const BaseLayouts = ({ children }) => {
  return (
    <div className="relative w-full flex flex-col p-2 gap-5">
      <div className="absolute right-7 top-4">
        <ModeToggle />
      </div>
      <div className="font-bold text-2xl mx-auto ">Users</div>
      <HeaderLayout />
      {children}
    </div>
  );
};

export default BaseLayouts;
