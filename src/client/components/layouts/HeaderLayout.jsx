import React from "react";
import { NavLink } from "react-router-dom";
import { Button } from "@/components/ui/button";

const HeaderLayout = () => {
  const headerNav = [
    { name: "User List", path: "/" },
    { name: "Add User", path: "/add-user" },
  ];
  return (
    <div className="flex gap-2 text-gray-800">
      {headerNav.map((nav) => (
        <Button asChild>
          <NavLink key={nav.name} to={nav.path}>
            {nav.name}
          </NavLink>
        </Button>
      ))}
    </div>
  );
};

export default HeaderLayout;
