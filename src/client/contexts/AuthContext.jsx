import React, { createContext, useContext, useEffect, useState } from "react";
import Cookies from "js-cookie";
import { toast } from "sonner";

const AuthContext = createContext({});

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Check if user is authenticated on app start
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = () => {
    try {
      const token = Cookies.get("auth_token");
      const userData = Cookies.get("user_data");

      if (token && userData) {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error("Error checking auth status:", error);
      // Clear invalid cookies
      Cookies.remove("auth_token");
      Cookies.remove("user_data");
    } finally {
      setLoading(false);
    }
  };

  const login = (token, userData) => {
    try {
      // Set cookies with secure options
      const cookieOptions = {
        expires: 7, // 7 days
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        path: "/",
      };

      Cookies.set("auth_token", token, cookieOptions);
      Cookies.set("user_data", JSON.stringify(userData), cookieOptions);

      setUser(userData);
      setIsAuthenticated(true);

      toast.success("Login successful!");
    } catch (error) {
      console.error("Login error:", error);
      toast.error("Login failed. Please try again.");
    }
  };

  const logout = () => {
    try {
      // Remove cookies
      Cookies.remove("auth_token");
      Cookies.remove("user_data");

      setUser(null);
      setIsAuthenticated(false);

      toast.success("Logged out successfully!");
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  const updateUser = (userData) => {
    try {
      const cookieOptions = {
        expires: 7,
        secure: process.env.NODE_ENV === "production",
        sameSite: "strict",
        path: "/",
      };

      Cookies.set("user_data", JSON.stringify(userData), cookieOptions);
      setUser(userData);
    } catch (error) {
      console.error("Update user error:", error);
    }
  };

  const value = {
    user,
    loading,
    isAuthenticated,
    login,
    logout,
    updateUser,
    checkAuthStatus,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export default AuthContext;
