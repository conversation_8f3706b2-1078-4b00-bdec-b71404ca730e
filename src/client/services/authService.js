import api from "./api";

export const authService = {
  // Register new user
  signup: async (userData) => {
    try {
      const response = await api.post("/users", userData);
      return {
        success: true,
        data: response.data,
        message: "Account created successfully!",
      };
    } catch (error) {
      throw {
        success: false,
        message: error.response?.data?.error || "Registration failed",
        error: error.response?.data,
      };
    }
  },

  // Login user
  login: async (credentials) => {
    try {
      const response = await api.post("/auth/login", credentials);
      return {
        success: true,
        data: response.data,
        message: "Login successful!",
      };
    } catch (error) {
      throw {
        success: false,
        message: error.response?.data?.error || "Login failed",
        error: error.response?.data,
      };
    }
  },

  // Logout user
  logout: async () => {
    try {
      await api.post("/auth/logout");
      return {
        success: true,
        message: "Logged out successfully!",
      };
    } catch (error) {
      // Even if logout fails on server, we should clear client state
      return {
        success: true,
        message: "Logged out successfully!",
      };
    }
  },

  // Get current user profile
  getProfile: async () => {
    try {
      const response = await api.get("/auth/profile");
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      throw {
        success: false,
        message: error.response?.data?.error || "Failed to fetch profile",
        error: error.response?.data,
      };
    }
  },

  // Refresh token
  refreshToken: async () => {
    try {
      const response = await api.post("/auth/refresh");
      return {
        success: true,
        data: response.data,
      };
    } catch (error) {
      throw {
        success: false,
        message: "Session expired",
        error: error.response?.data,
      };
    }
  },
};

export default authService;
