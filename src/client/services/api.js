import axios from "axios";
import Cookies from "js-cookie";
import { toast } from "sonner";

// Create axios instance
const api = axios.create({
  baseURL: "/api",
  headers: {
    "Content-Type": "application/json",
  },
  withCredentials: true,
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get("auth_token");
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors and token refresh
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      // Clear auth data on 401
      Cookies.remove("auth_token");
      Cookies.remove("user_data");

      // Redirect to login
      toast.error("Session expired. Please login again.");
      window.location.href = "/auth/login";

      return Promise.reject(error);
    }

    if (error.response?.status === 403) {
      toast.error(
        "Access denied. You do not have permission to perform this action."
      );
    }

    if (error.response?.status >= 500) {
      toast.error("Server error. Please try again later.");
    }

    return Promise.reject(error);
  }
);

export default api;
