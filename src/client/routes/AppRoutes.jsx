import React from "react";
import { Routes, Route, Navigate } from "react-router-dom";

import AuthGuard from "../components/AuthGuard";
import ProtectedRoute from "../components/ProtectedRoute";
import DashboardLayout from "../components/layouts/DashboardLayout";

// Auth pages
import Login from "../pages/auth/Login";
import Signup from "../pages/auth/Signup";

// Protected pages
import Dashboard from "../pages/Dashboard";
import { UserList } from "../pages/UserList";
import { AddUser } from "../pages/AddUser";

const AppRoutes = () => {
  return (
    <Routes>
      {/* Redirect root to dashboard */}
      <Route path="/" element={<Navigate to="/dashboard" replace />} />

      {/* Auth routes - only accessible when not authenticated */}
      <Route
        path="/auth/login"
        element={
          <AuthGuard>
            <Login />
          </AuthGuard>
        }
      />
      <Route
        path="/auth/signup"
        element={
          <AuthGuard>
            <Signup />
          </AuthGuard>
        }
      />

      {/* Protected routes - only accessible when authenticated */}
      <Route
        path="/dashboard"
        element={
          <ProtectedRoute>
            <DashboardLayout />
          </ProtectedRoute>
        }
      >
        <Route index element={<Dashboard />} />
        <Route path="users" element={<UserList />} />
        <Route path="add-user" element={<AddUser />} />
      </Route>

      {/* Legacy routes for backward compatibility */}
      <Route
        path="/add-user"
        element={
          <ProtectedRoute>
            <AddUser />
          </ProtectedRoute>
        }
      />

      {/* Catch all - redirect to dashboard */}
      <Route path="*" element={<Navigate to="/dashboard" replace />} />
    </Routes>
  );
};

export default AppRoutes;
